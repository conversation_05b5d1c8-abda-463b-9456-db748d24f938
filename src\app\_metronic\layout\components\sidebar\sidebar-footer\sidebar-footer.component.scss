.sidebar-language-toggle {
  .language-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #6c757d;
    transition: color 0.3s ease;
  }

  .form-check {
    margin-bottom: 0;
  }

  .language-switch {
    width: 2.5rem;
    height: 1.25rem;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;

    &:checked {
      background-color: #28a745;
      border-color: #28a745;
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
      border-color: #28a745;
    }
  }

  .form-check-label {
    margin-left: 0.5rem;
    cursor: pointer;

    i {
      transition: color 0.3s ease;
    }
  }

  // RTL Support
  :host-context(html[dir="rtl"]) &,
  :host-context(html[lang="ar"]) & {
    .d-flex {
      flex-direction: row-reverse;
    }

    .language-label {
      margin-left: 0.5rem;
      margin-right: 0;
    }

    .form-check-label {
      margin-left: 0;
      margin-right: 0.5rem;
    }
  }

  // Hover effects
  &:hover {
    .language-label {
      color: #28a745;
    }

    .language-switch:not(:checked) {
      border-color: #28a745;
    }
  }

  // Minimize mode adjustments
  .app-sidebar-minimize & {
    .language-label {
      display: none;
    }

    .d-flex {
      justify-content: center !important;
    }
  }
}
import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';
import { environment } from '../../../../../../environments/environment';
import { AuthenticationService } from 'src/app/pages/authentication';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';
import { TranslationService } from '../../../../../modules/i18n/translation.service';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-sidebar-footer',
  templateUrl: './sidebar-footer.component.html',
  styleUrls: ['./sidebar-footer.component.scss'],
})
export class SidebarFooterComponent implements OnInit, OnDestroy {
  appPreviewChangelogUrl: string = environment.appPreviewChangelogUrl;
  currentLanguage: string = 'en';
  private destroy$ = new Subject<void>();

  constructor(
    private authenticationService: AuthenticationService,
    private cd: ChangeDetectorRef,
    private router: Router,
    private translationService: TranslationService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    // Subscribe to language changes
    this.translationService.currentLanguage$
      .pipe(takeUntil(this.destroy$))
      .subscribe(lang => {
        this.currentLanguage = lang;
        this.cd.detectChanges();
      });

    // Initialize current language
    this.currentLanguage = this.translationService.getCurrentLanguage();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleLanguage(): void {
    const newLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
    this.translationService.setLanguage(newLanguage);
  }

  getCurrentLanguageLabel(): string {
    return this.currentLanguage === 'ar' ? 'العربية' : 'English';
  }

  logout(){
    // Clear all authentication data
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');

    // Try to call the logout API if available
    this.authenticationService.logout().subscribe(
      (response: any) => {
        console.log('logout success:', response);
        this.authenticationService.setCurrentUser(null);
        this.cd.markForCheck();
        this.router.navigate(['/authentication/login']);
      },
      (error: any) => {
        console.error('Failed to logout:', error);
        // Even if API call fails, still logout locally
        this.authenticationService.setCurrentUser(null);
        this.cd.markForCheck();
        this.router.navigate(['/authentication/login']);
      }
    );
  }
}
